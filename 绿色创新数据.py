#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿色专利数据合并与清洗脚本
功能：将2014-2023年的上市公司、子公司绿色专利数据合并并进行数据清洗
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_merge_data():
    """
    加载并合并2014-2023年的绿色专利数据
    """
    print("开始加载数据文件...")
    
    # 存储所有年份的数据
    all_data = []
    
    # 遍历2014-2023年的数据文件
    for year in range(2014, 2024):
        file_path = f'csv_data/上市公司、子公司绿色专利数据{year}.xlsx'
        
        if os.path.exists(file_path):
            print(f"正在加载 {year} 年数据...")
            try:
                df = pd.read_excel(file_path)
                # 添加数据年份标识
                df['数据年份'] = year
                all_data.append(df)
                print(f"  - {year}年数据加载成功，共 {len(df)} 条记录")
            except Exception as e:
                print(f"  - 加载{year}年数据时出错: {e}")
        else:
            print(f"  - {year}年数据文件不存在: {file_path}")
    
    if not all_data:
        raise ValueError("没有找到任何数据文件！")
    
    # 合并所有数据
    print("\n正在合并数据...")
    merged_data = pd.concat(all_data, ignore_index=True)
    print(f"合并完成，总计 {len(merged_data)} 条记录")
    
    return merged_data

def clean_data(df):
    """
    数据清洗函数
    """
    print("\n开始数据清洗...")
    original_count = len(df)
    
    # 1. 处理列名中的空格和特殊字符
    print("1. 清理列名...")
    df.columns = df.columns.str.strip()
    # 修正可能的列名问题
    column_mapping = {
        '关联股票代 码': '关联股票代码',
        '专利英 文名称': '专利英文名称',
        '国际 申请号': '国际申请号'
    }
    df = df.rename(columns=column_mapping)
    
    # 2. 删除完全重复的记录
    print("2. 删除重复记录...")
    before_dedup = len(df)
    df = df.drop_duplicates()
    after_dedup = len(df)
    print(f"   删除了 {before_dedup - after_dedup} 条重复记录")
    
    # 3. 处理缺失值
    print("3. 处理缺失值...")
    # 显示缺失值统计
    missing_stats = df.isnull().sum()
    print("   各列缺失值统计:")
    for col, missing_count in missing_stats.items():
        if missing_count > 0:
            missing_pct = (missing_count / len(df)) * 100
            print(f"     {col}: {missing_count} ({missing_pct:.2f}%)")
    
    # 删除关键字段全部为空的记录
    key_columns = ['专利申请号', '申请人', '专利中文名称']
    before_key_clean = len(df)
    df = df.dropna(subset=key_columns, how='all')
    after_key_clean = len(df)
    print(f"   删除关键字段全为空的记录: {before_key_clean - after_key_clean} 条")
    
    # 4. 数据类型转换和标准化
    print("4. 数据类型转换...")
    
    # 处理日期字段
    date_columns = ['申请日期', '公开日期', '授权日期', '最早优先权日期']
    for col in date_columns:
        if col in df.columns:
            # 将数字格式的日期转换为标准日期格式
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # 假设日期格式为YYYYMMDD
            df[col] = df[col].apply(lambda x: pd.to_datetime(str(int(x)), format='%Y%m%d', errors='coerce') if pd.notna(x) else x)
    
    # 处理年份字段
    year_columns = ['申请年份', '公开年份', '授权年份']
    for col in year_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 5. 数据验证和异常值处理
    print("5. 数据验证...")
    
    # 检查年份的合理性
    current_year = datetime.now().year
    for col in year_columns:
        if col in df.columns:
            # 删除年份不合理的记录（如小于1980或大于当前年份）
            before_year_clean = len(df)
            df = df[(df[col].isna()) | ((df[col] >= 1980) & (df[col] <= current_year))]
            after_year_clean = len(df)
            if before_year_clean != after_year_clean:
                print(f"   删除{col}异常的记录: {before_year_clean - after_year_clean} 条")
    
    # 6. 文本字段清理
    print("6. 文本字段清理...")
    text_columns = ['关联企业名称', '申请人', '专利中文名称', '专利英文名称']
    for col in text_columns:
        if col in df.columns:
            # 去除前后空格
            df[col] = df[col].astype(str).str.strip()
            # 将'nan'字符串转换为NaN
            df[col] = df[col].replace(['nan', 'NaN', 'NULL', ''], np.nan)
    
    # 7. 股票代码标准化
    print("7. 股票代码标准化...")
    if '关联股票代码' in df.columns:
        # 去除股票代码中的非数字字符，保留6位数字
        df['关联股票代码'] = df['关联股票代码'].astype(str).str.extract('(\d{6})')
    
    print(f"\n数据清洗完成！")
    print(f"原始记录数: {original_count}")
    print(f"清洗后记录数: {len(df)}")
    print(f"删除记录数: {original_count - len(df)}")
    
    return df

def generate_summary_report(df):
    """
    生成数据摘要报告
    """
    print("\n" + "="*50)
    print("数据摘要报告")
    print("="*50)
    
    # 基本统计
    print(f"总记录数: {len(df):,}")
    print(f"总列数: {len(df.columns)}")
    
    # 按年份统计
    if '数据年份' in df.columns:
        print("\n按年份分布:")
        year_stats = df['数据年份'].value_counts().sort_index()
        for year, count in year_stats.items():
            print(f"  {year}年: {count:,} 条")
    
    # 按专利类型统计
    if '专利类型' in df.columns:
        print("\n按专利类型分布:")
        type_stats = df['专利类型'].value_counts()
        for ptype, count in type_stats.head(10).items():
            print(f"  {ptype}: {count:,} 条")
    
    # 按绿色专利大类统计
    if '绿色专利大类' in df.columns:
        print("\n按绿色专利大类分布:")
        category_stats = df['绿色专利大类'].value_counts()
        for category, count in category_stats.head(10).items():
            print(f"  {category}: {count:,} 条")
    
    # 数据质量统计
    print("\n数据质量统计:")
    total_cells = len(df) * len(df.columns)
    missing_cells = df.isnull().sum().sum()
    completeness = ((total_cells - missing_cells) / total_cells) * 100
    print(f"  数据完整度: {completeness:.2f}%")
    print(f"  缺失值总数: {missing_cells:,}")

def main():
    """
    主函数
    """
    try:
        # 1. 加载并合并数据
        merged_data = load_and_merge_data()
        
        # 2. 数据清洗
        cleaned_data = clean_data(merged_data)
        
        # 3. 保存清洗后的数据
        output_file = 'csv_data/merged_cleaned_green_patents_2014_2023.xlsx'
        print(f"\n正在保存清洗后的数据到: {output_file}")
        cleaned_data.to_excel(output_file, index=False)
        print("数据保存成功！")
        
        # 4. 同时保存为CSV格式
        csv_output_file = 'csv_data/merged_cleaned_green_patents_2014_2023.csv'
        print(f"同时保存CSV格式到: {csv_output_file}")
        cleaned_data.to_csv(csv_output_file, index=False, encoding='utf-8-sig')
        print("CSV文件保存成功！")
        
        # 5. 生成摘要报告
        generate_summary_report(cleaned_data)
        
        print("\n处理完成！")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
